"""PI Lawyer AI package."""

# Make sure submodules are properly importable
# This helps tests find all the modules they need

# Import main subpackages to make them available through pi_lawyer.*
# Temporarily disabled to avoid import conflicts with new agent structure
# try:
#     from . import agents, api, auth, config, db, models, services, utils
# except ImportError as e:
#     import warnings
#     warnings.warn(f"Error importing submodule: {e}")

# Version information
__version__ = '0.1.0'
