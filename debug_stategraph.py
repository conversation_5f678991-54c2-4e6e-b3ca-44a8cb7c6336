#!/usr/bin/env python3
"""
Debug StateGraph import issues.
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def debug_stategraph_imports():
    """Debug StateGraph imports from different locations."""
    print("🔍 Debugging StateGraph Imports...")
    print("=" * 50)
    
    # Test 1: Direct LangGraph import
    print("\n1. Direct LangGraph Import:")
    try:
        from langgraph.graph import StateGraph
        print(f"✅ StateGraph imported: {StateGraph}")
        print(f"   Module: {StateGraph.__module__}")
        print(f"   File: {StateGraph.__module__.__file__ if hasattr(StateGraph.__module__, '__file__') else 'N/A'}")
        
        # Test StateGraph signature
        import inspect
        sig = inspect.signature(StateGraph.__init__)
        print(f"   Signature: {sig}")
        
    except Exception as e:
        print(f"❌ Direct import failed: {e}")
    
    # Test 2: Import from base agent
    print("\n2. Import from Base Agent:")
    try:
        from backend.agents.shared.core.base_agent import StateGraph
        print(f"✅ StateGraph from base_agent: {StateGraph}")
        print(f"   Module: {StateGraph.__module__}")
    except Exception as e:
        print(f"❌ Base agent import failed: {e}")
    
    # Test 3: Import from deadline agent
    print("\n3. Import from Deadline Agent:")
    try:
        # Import the deadline agent module to see what StateGraph it uses
        import backend.agents.insights.deadline.agent as deadline_module
        
        # Check if StateGraph is available in the module
        if hasattr(deadline_module, 'StateGraph'):
            StateGraph = deadline_module.StateGraph
            print(f"✅ StateGraph from deadline agent: {StateGraph}")
            print(f"   Module: {StateGraph.__module__}")
        else:
            print("❌ StateGraph not found in deadline agent module")
            
    except Exception as e:
        print(f"❌ Deadline agent import failed: {e}")
    
    # Test 4: Check what's in sys.modules
    print("\n4. StateGraph in sys.modules:")
    langgraph_modules = [k for k in sys.modules.keys() if 'langgraph' in k.lower()]
    print(f"   LangGraph modules loaded: {langgraph_modules}")
    
    # Test 5: Try different StateGraph creation patterns
    print("\n5. Testing StateGraph Creation Patterns:")
    try:
        from langgraph.graph import StateGraph
        from typing import Dict, Any, TypedDict
        
        # Pattern 1: No arguments
        try:
            graph1 = StateGraph()
            print("✅ StateGraph() works")
        except Exception as e:
            print(f"❌ StateGraph() failed: {e}")
        
        # Pattern 2: With TypedDict
        class TestState(TypedDict):
            messages: list
            
        try:
            graph2 = StateGraph(TestState)
            print("✅ StateGraph(TypedDict) works")
        except Exception as e:
            print(f"❌ StateGraph(TypedDict) failed: {e}")
            
        # Pattern 3: With state_schema parameter
        try:
            graph3 = StateGraph(state_schema=TestState)
            print("✅ StateGraph(state_schema=TypedDict) works")
        except Exception as e:
            print(f"❌ StateGraph(state_schema=TypedDict) failed: {e}")
            
    except Exception as e:
        print(f"❌ StateGraph creation tests failed: {e}")

if __name__ == "__main__":
    debug_stategraph_imports()
