#!/usr/bin/env python3
"""
Test LangGraph StateGraph usage to identify the issue.
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_langgraph_import():
    """Test basic LangGraph imports."""
    try:
        from langgraph.graph import StateGraph
        print("✅ StateGraph import successful")
        return True
    except Exception as e:
        print(f"❌ StateGraph import failed: {e}")
        return False

def test_state_graph_creation():
    """Test StateGraph creation with different approaches."""
    try:
        from langgraph.graph import StateGraph
        from typing import Dict, Any
        
        # Test 1: StateGraph with no arguments (new API)
        try:
            graph1 = StateGraph()
            print("✅ StateGraph() - no arguments works")
        except Exception as e:
            print(f"❌ StateGraph() - no arguments failed: {e}")
        
        # Test 2: StateGraph with Dict[str, Any] (common pattern)
        try:
            graph2 = StateGraph(Dict[str, Any])
            print("✅ StateGraph(Dict[str, Any]) works")
        except Exception as e:
            print(f"❌ StateGraph(Dict[str, Any]) failed: {e}")
            
        # Test 3: StateGraph with state_schema parameter (newer API)
        try:
            graph3 = StateGraph(state_schema=Dict[str, Any])
            print("✅ StateGraph(state_schema=Dict[str, Any]) works")
        except Exception as e:
            print(f"❌ StateGraph(state_schema=Dict[str, Any]) failed: {e}")
            
        return True
        
    except Exception as e:
        print(f"❌ StateGraph creation test failed: {e}")
        return False

def test_base_state_import():
    """Test importing the base state."""
    try:
        from backend.agents.shared.core.state import BaseLangGraphState
        print("✅ BaseLangGraphState import successful")
        print(f"   Type: {type(BaseLangGraphState)}")
        return True
    except Exception as e:
        print(f"❌ BaseLangGraphState import failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing LangGraph StateGraph Usage...")
    print("=" * 50)
    
    # Test imports
    langgraph_ok = test_langgraph_import()
    state_ok = test_base_state_import()
    
    if langgraph_ok:
        test_state_graph_creation()
    
    print("\n" + "=" * 50)
    print("📊 Test Complete")

if __name__ == "__main__":
    main()
