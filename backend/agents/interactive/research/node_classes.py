"""
AiLex Research Agent Node Classes

This module contains the node classes for the AiLex research agent.
Each class represents a node in the research workflow and inherits from BaseAgent.
"""

import logging
from typing import Any, Dict

from langchain_core.runnables import RunnableConfig

from backend.agents.interactive.research.state import ResearchState
from backend.agents.shared.core.base_agent import BaseAgent

# Set up logging
logger = logging.getLogger(__name__)


class QueryGenNode(BaseAgent):
    """
    Node for generating optimized search queries from the user's question.
    
    This node uses an LLM to reformulate the user's question into:
    1. 2-3 vector search queries (reformulations that preserve intent but use legal terminology)
    2. 1 keyword/boolean search query (for traditional search)
    """
    
    def __init__(self, agent_name: str = "researchAgent", node_name: str = "query_gen"):
        """
        Initialize the query generation node.
        
        Args:
            agent_name: Name of the agent
            node_name: Name of the node
        """
        super().__init__(agent_name=agent_name, node_name=node_name)
    
    async def execute(self, state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
        """
        Execute the query generation node.
        
        Args:
            state: Current state
            config: Runnable configuration
            
        Returns:
            Updated state
        """
        logger.info("Executing query generation node")
        
        # Get the research state
        research_state = ResearchState(**state)
        
        # Get the LLM client
        llm = await self._llm(state)
        
        # Generate optimized search queries
        try:
            # Prepare the prompt
            prompt = f"""
            Generate optimized search queries for the following legal research question:
            
            Question: {research_state.question}
            
            Jurisdiction: {research_state.jurisdiction}
            Practice Areas: {', '.join(research_state.practice_areas) if research_state.practice_areas else 'General'}
            
            Please provide:
            1. 2-3 vector search queries (reformulations that preserve intent but use legal terminology)
            2. 1 keyword/boolean search query (for traditional search)
            
            Format your response as JSON:
            {{
                "vector_queries": ["query1", "query2", "query3"],
                "keyword_query": "keyword query"
            }}
            """
            
            # Call the LLM
            response = await llm.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                temperature=0.2,
                max_tokens=500,
                response_format={"type": "json_object"}
            )
            
            # Extract the content from the response
            content = response.get("choices", [{}])[0].get("message", {}).get("content", "{}")
            
            # Parse the JSON response
            import json
            result = json.loads(content)
            
            # Update the state with the generated queries
            research_state.queries = result.get("vector_queries", [])
            research_state.keyword_query = result.get("keyword_query", "")
            
            logger.info(f"Generated {len(research_state.queries)} vector queries and 1 keyword query")
            
            # Return success
            return {**research_state.to_dict(), "status": "success", "next": "vector_search"}
        except Exception as e:
            logger.error(f"Error in query generation: {str(e)}")
            return {**research_state.to_dict(), "status": "error", "error": str(e), "next": "error"}
    
    async def initialize(self, state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
        """Initialize the node."""
        return state
    
    async def cleanup(self, state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
        """Clean up the node."""
        return state


class RerankNode(BaseAgent):
    """
    Node for reranking search results.
    
    This node uses a reranking model to rerank the search results based on relevance
    to the original query.
    """
    
    def __init__(self, agent_name: str = "researchAgent", node_name: str = "rerank"):
        """
        Initialize the reranking node.
        
        Args:
            agent_name: Name of the agent
            node_name: Name of the node
        """
        super().__init__(agent_name=agent_name, node_name=node_name)
    
    async def execute(self, state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
        """
        Execute the reranking node.
        
        Args:
            state: Current state
            config: Runnable configuration
            
        Returns:
            Updated state
        """
        logger.info("Executing reranking node")
        
        # Get the research state
        research_state = ResearchState(**state)
        
        # Skip if no documents found
        if not research_state.legal_documents:
            logger.info("No documents to rerank, skipping reranking")
            return {**research_state.to_dict(), "status": "success", "next": "collect_citations"}
        
        # Get the LLM client
        llm = await self._llm(state)
        
        # Rerank the documents
        try:
            # TODO: Implement reranking logic using the LLM
            # For now, just pass through the documents
            
            logger.info(f"Reranked {len(research_state.legal_documents)} documents")
            
            # Return success
            return {**research_state.to_dict(), "status": "success", "next": "collect_citations"}
        except Exception as e:
            logger.error(f"Error in reranking: {str(e)}")
            return {**research_state.to_dict(), "status": "error", "error": str(e), "next": "error"}
    
    async def initialize(self, state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
        """Initialize the node."""
        return state
    
    async def cleanup(self, state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
        """Clean up the node."""
        return state


class ExitGuardNode(BaseAgent):
    """
    Node for ensuring answer is legally relevant and redacting sensitive information.
    
    This node validates the generated answer to ensure it's legally relevant,
    properly formatted with citations, and doesn't contain sensitive information.
    """
    
    def __init__(self, agent_name: str = "researchAgent", node_name: str = "exit_guard"):
        """
        Initialize the exit guard node.
        
        Args:
            agent_name: Name of the agent
            node_name: Name of the node
        """
        super().__init__(agent_name=agent_name, node_name=node_name)
    
    async def execute(self, state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
        """
        Execute the exit guard node.
        
        Args:
            state: Current state
            config: Runnable configuration
            
        Returns:
            Updated state
        """
        logger.info("Executing exit guard node")
        
        # Get the research state
        research_state = ResearchState(**state)
        
        # Skip validation if no answer was generated
        if not research_state.answer:
            return {**research_state.to_dict(), "status": "success", "next": "FINISH"}
        
        # Get the LLM client
        llm = await self._llm(state)
        
        # Validate the answer
        try:
            # Prepare the prompt
            prompt = f"""
            Validate the following legal research answer:
            
            Question: {research_state.question}
            Answer: {research_state.answer}
            
            Please check for:
            1. Legal relevance
            2. Proper citation formatting
            3. Sensitive information that should be redacted
            
            If any issues are found, please provide a corrected version.
            
            Format your response as JSON:
            {{
                "is_valid": true/false,
                "issues": ["issue1", "issue2"],
                "corrected_answer": "corrected answer if needed"
            }}
            """
            
            # Call the LLM
            response = await llm.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                temperature=0.0,
                max_tokens=1000,
                response_format={"type": "json_object"}
            )
            
            # Extract the content from the response
            content = response.get("choices", [{}])[0].get("message", {}).get("content", "{}")
            
            # Parse the JSON response
            import json
            result = json.loads(content)
            
            # Update the answer if needed
            if not result.get("is_valid", True) and result.get("corrected_answer"):
                research_state.answer = result.get("corrected_answer")
                logger.info(f"Answer corrected: {', '.join(result.get('issues', []))}")
            
            # Return success
            return {**research_state.to_dict(), "status": "success", "next": "FINISH"}
        except Exception as e:
            logger.error(f"Error in exit guard: {str(e)}")
            return {**research_state.to_dict(), "status": "error", "error": str(e), "next": "FINISH"}
    
    async def initialize(self, state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
        """Initialize the node."""
        return state
    
    async def cleanup(self, state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
        """Clean up the node."""
        return state
